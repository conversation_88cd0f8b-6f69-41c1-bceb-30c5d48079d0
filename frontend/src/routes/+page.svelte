<script lang="ts">
  import { onMount } from 'svelte';
  import ThemeToggle from '$components/ui/ThemeToggle.svelte';
  import PersonaSelector from '$components/PersonaSelector.svelte';

  let wsConnection: WebSocket | null = null;
  let connectionStatus = 'Disconnected';
  let messages: Array<{
    sender: 'therapist' | 'patient';
    content: string;
    timestamp: string;
    thinking?: string;
    metadata?: any;
  }> = [];
  let therapistThoughts: Array<{ content: string; timestamp: string }> = [];
  let patientThoughts: Array<{ content: string; timestamp: string }> = [];

  // Profile data
  let therapistProfile = {
    name: "Dr. <PERSON>",
    credentials: "PhD, LCSW",
    specialization: "Cognitive Behavioral Therapy",
    approach: "Evidence-based, empathetic",
    experience: "12 years",
    sessions: "2,400+ completed"
  };

  let patientProfile = {
    name: "<PERSON>",
    age: "28",
    sessionHistory: "Session #3",
    background: "Anxiety management",
    traits: "Introspective, goal-oriented",
    lastSession: "2 weeks ago"
  };

  // Selected persona details for display
  let selectedPersonaDetails: any = null;

  // Analytics data - tracks pre and post conversation metrics
  let preConversationAnalytics = {
    sentiment: null,
    motivationLevel: null,
    engagementLevel: null
  };

  let postConversationAnalytics = {
    sentiment: null,
    motivationLevel: null,
    engagementLevel: null
  };

  // Dummy messages for testing
  // messages = [
  //   {
  //     sender: 'therapist',
  //     content: 'Test Message',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'Test Thinking',
  //     metadata: {
  //       sentiment: 'positive',
  //       motivationLevel: 'high',
  //       engagementLevel: 'high',
  //       confidence: 0.9,
  //       processingTime: 0
  //     }
  //   },
  //   {
  //     sender: 'patient',
  //     content: 'Test Message',
  //     timestamp: new Date().toISOString(),
  //     thinking: 'Test Thinking',
  //     metadata: {
  //       sentiment: 'positive',
  //       motivationLevel: 'high',
  //       engagementLevel: 'high',
  //       confidence: 0.9,
  //       processingTime: 0
  //     }
  //   }
  // ];
  // therapistThoughts = [
  //   {
  //     content: 'Test Thinking',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  // patientThoughts = [
  //   {
  //     content: 'Test Thinking',
  //     timestamp: new Date().toISOString()
  //   }
  // ];
  
  // Configuration state
  let maxTurns = 20;
  let conversationActive = false;
  let conversationId: string | null = null;
  let selectedPersonaId: string | null = null;
  let showPersonaSelector = false;
  
  onMount(() => {
    connectWebSocket();
    
    return () => {
      if (wsConnection) {
        wsConnection.close();
      }
    };
  });
  
  function connectWebSocket() {
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3000';
    wsConnection = new WebSocket(`${wsUrl}/ws`);
    
    wsConnection.onopen = () => {
      connectionStatus = 'Connected';
      console.log('🔌 WebSocket connected successfully');
    };

    wsConnection.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('📨 Received WebSocket message:', data);

        handleWebSocketMessage(data);
      } catch (error) {
        console.error('❌ Error parsing WebSocket message:', error);
      }
    };
    
    wsConnection.onclose = () => {
      connectionStatus = 'Disconnected';
      console.log('WebSocket disconnected');
    };
    
    wsConnection.onerror = (error) => {
      connectionStatus = 'error';
      console.error('WebSocket error:', error);
    };
  }
  
  function handleWebSocketMessage(data: any) {
    console.log(`🔄 Handling message type: ${data.type}`);

    switch (data.type) {
      case 'welcome':
        console.log('👋 Welcome message received');
        break;

      case 'conversation_created':
        console.log('✅ Conversation created:', data.data);
        conversationId = data.data.conversationId;
        break;

      case 'conversation_started':
        console.log('🚀 Conversation started:', data.data);
        break;

      case 'conversation_message':
        console.log('💬 Conversation message received:', data.data);
        handleConversationMessage(data.data);
        break;

      case 'conversation_ended':
        console.log('🏁 Conversation ended:', data.data);
        conversationActive = false;
        console.log('messages:', messages);
        break;

      case 'conversation_paused':
        console.log('⏸️ Conversation paused');
        break;

      case 'conversation_resumed':
        console.log('▶️ Conversation resumed');
        break;

      case 'conversation_cleared':
        console.log('🗑️ Conversation cleared');
        messages = [];
        therapistThoughts = [];
        patientThoughts = [];
        break;

      case 'error':
        console.error('❌ Server error:', data.message);
        break;

      default:
        console.warn('⚠️ Unknown message type:', data.type);
        // For backward compatibility, treat unknown messages as general messages
        if (data.message) {
          messages = [...messages, {
            sender: 'therapist', // Default sender
            content: data.message,
            timestamp: data.timestamp || new Date().toISOString()
          }];
        }
    }
  }

  function handleConversationMessage(messageData: any) {
    console.log('📝 Processing conversation message:', messageData);

    if (messageData.message) {
      // Add conversation message
      messages = [...messages, {
        sender: messageData.message.sender,
        content: messageData.message.content,
        timestamp: messageData.message.timestamp,
        thinking: messageData.thinking?.content,
        metadata: messageData.metadata
      }];

      // Update analytics based on therapist's analysis of patient
      if (messageData.message.sender === 'therapist' && messageData.metadata?.patientAnalysis) {
        const analysis = messageData.metadata.patientAnalysis;

        // Set pre-conversation analytics from second therapist message
        if (messages.length === 3) {
          preConversationAnalytics = {
            sentiment: analysis.sentiment,
            motivationLevel: analysis.motivationLevel,
            engagementLevel: analysis.engagementLevel
          };
        }

        // Always update post-conversation analytics with latest analysis
        postConversationAnalytics = {
          sentiment: analysis.sentiment,
          motivationLevel: analysis.motivationLevel,
          engagementLevel: analysis.engagementLevel
        };
      }

      console.log(`💭 ${messageData.message.sender} said: "${messageData.message.content}"`);
    }

    if (messageData.thinking) {
      // Add thinking to appropriate array
      const thought = {
        content: messageData.thinking.content,
        timestamp: messageData.thinking.timestamp
      };

      if (messageData.thinking.agent === 'therapist') {
        therapistThoughts = [...therapistThoughts, thought];
        console.log(`🧠 Therapist thinking: "${thought.content}"`);
      } else if (messageData.thinking.agent === 'patient') {
        patientThoughts = [...patientThoughts, thought];
        console.log(`💭 Patient thinking: "${thought.content}"`);
      }
    }
  }

  async function handlePersonaSelect(personaId: string) {
    selectedPersonaId = personaId;
    showPersonaSelector = false;
    console.log('🎭 Selected persona:', personaId);

    // Fetch persona details for display
    try {
      const response = await fetch(`/api/personas/${personaId}`);
      const result = await response.json();
      if (result.success) {
        selectedPersonaDetails = result.data;
        // Update patient profile display
        patientProfile = {
          name: result.data.name,
          age: result.data.age.toString(),
          sessionHistory: result.data.sessionContext,
          background: result.data.background,
          traits: result.data.conversationalStyle,
          lastSession: "Starting new session"
        };
      }
    } catch (error) {
      console.error('Error fetching persona details:', error);
    }
  }

  function startConversation() {
    console.log('🚀 Starting conversation...');
    console.log(`⚙️ Configuration: maxTurns=${maxTurns}, personaId=${selectedPersonaId}`);

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
      conversationActive = true;

      const message = {
        type: 'start_conversation',
        config: {
          maxTurns,
          personaId: selectedPersonaId
        }
      };

      console.log('📤 Sending start conversation message:', message);
      wsConnection.send(JSON.stringify(message));
    } else {
      console.error('❌ WebSocket not connected');
    }
  }

  function clearConversation() {
    console.log('🗑️ Clearing conversation...');

    if (wsConnection && wsConnection.readyState === WebSocket.OPEN && conversationId) {
      wsConnection.send(JSON.stringify({
        type: 'clear_conversation'
      }));
    }

    // Clear local state
    messages = [];
    therapistThoughts = [];
    patientThoughts = [];
    conversationActive = false;
    conversationId = null;

    // Reset analytics
    preConversationAnalytics = {
      sentiment: null,
      motivationLevel: null,
      engagementLevel: null
    };
    postConversationAnalytics = {
      sentiment: null,
      motivationLevel: null,
      engagementLevel: null
    };
  }
</script>

<svelte:head>
  <title>MiCA</title>
</svelte:head>

<div class="min-h-screen bg-neutral-50 dark:bg-neutral-900 theme-transition">
  <!-- Header -->
  <header class="bg-white dark:bg-neutral-800 shadow-sm border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <h1 class="text-2xl font-bold text-neutral-900 dark:text-neutral-100 theme-transition">MiCA</h1>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Theme Toggle -->
          <ThemeToggle variant="button" size="md" />

          <!-- WebSocket Status -->
          <div class="flex items-center space-x-2">
            <span class="text-sm text-neutral-600 dark:text-neutral-400 theme-transition">WebSocket Status:</span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {
              connectionStatus === 'Connected' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
              connectionStatus === 'Error' ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200' :
              'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
            } theme-transition">
              {connectionStatus}
            </span>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Configuration Panel -->
  <div class="bg-white dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 theme-transition">
    <div class="mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <label for="maxTurns" class="label">Max Turns:</label>
            <input
              id="maxTurns"
              type="number"
              bind:value={maxTurns}
              min="1"
              max="100"
              class="input w-20"
              disabled={conversationActive}
            />
          </div>
          <div class="flex items-center space-x-2">
            <button
              on:click={() => showPersonaSelector = !showPersonaSelector}
              disabled={conversationActive}
              class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {selectedPersonaId ? 'Change Persona' : 'Select Persona'}
            </button>
            {#if selectedPersonaId}
              <span class="text-sm text-neutral-600 dark:text-neutral-400">
                Persona selected
              </span>
            {/if}
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <button
            on:click={startConversation}
            disabled={conversationActive || connectionStatus !== 'Connected'}
            class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {conversationActive ? 'Running...' : 'Start'}
          </button>
          <button
            on:click={clearConversation}
            disabled={conversationActive}
            class="btn btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Persona Selector Modal -->
  {#if showPersonaSelector}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div class="bg-white dark:bg-neutral-800 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
              Select Patient Persona
            </h2>
            <button
              on:click={() => showPersonaSelector = false}
              class="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <PersonaSelector
            {selectedPersonaId}
            onPersonaSelect={handlePersonaSelect}
          />
        </div>
      </div>
    </div>
  {/if}

  <!-- Main Content - Three Panes -->
  <div class="mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
      
      <!-- Left Pane: Therapist Profile & Thinking -->
      <div class="card p-6 flex flex-col">
        <!-- Therapist Profile Section -->
        <div class="profile-section">
          <div class="profile-header">
            <!-- <div class="profile-avatar">
              {therapistProfile.name.split(' ').map(n => n[0]).join('')}
            </div> -->
            <div>
              <h3 class="profile-name">{therapistProfile.name}</h3>
              <p class="profile-title">{therapistProfile.credentials}</p>
            </div>
          </div>
          <div class="profile-details">
            <div class="profile-detail-item">
              <span class="profile-detail-label">Specialization:</span>
              <span class="profile-detail-value">{therapistProfile.specialization}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Approach:</span>
              <span class="profile-detail-value">{therapistProfile.approach}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Experience:</span>
              <span class="profile-detail-value">{therapistProfile.experience}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Sessions:</span>
              <span class="profile-detail-value">{therapistProfile.sessions}</span>
            </div>
          </div>
        </div>

        <!-- Therapist Thinking Section -->
        <div class="flex-1 flex flex-col">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
            Therapist Thinking
          </h2>
          <div class="space-y-3 flex-1 overflow-y-auto">
          {#each therapistThoughts as thought}
            <div class="thinking-therapist dark:bg-primary-900/20 dark:text-blue-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if therapistThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Therapist thinking will appear here during conversation...</p>
            </div>
          {/if}
          </div>
        </div>
      </div>

      <!-- Middle Pane: Conversation & Analytics -->
      <div class="card p-6 flex flex-col">
        <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
          Conversation
        </h2>
        <!-- Conversation Messages -->
        <div class="space-y-4 flex-1 overflow-y-auto">
          {#each messages as message}
            <div class="flex flex-col space-y-1">
              <div class="text-xs text-neutral-500 dark:text-neutral-400 flex items-center space-x-2 theme-transition">
                <span class="font-medium capitalize {message.sender === 'therapist' ? 'text-primary-600 dark:text-primary-400' : 'text-secondary-600 dark:text-secondary-400'} theme-transition">
                  {message.sender === 'therapist' ? '👩‍⚕️ Dr. Chen' : '👤 Alex'}
                </span>
                <span>•</span>
                <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
              </div>
              <div class="message-bubble {message.sender === 'therapist' ? 'bg-primary-50 dark:bg-primary-900/20 border-l-4 border-primary-500' : 'bg-secondary-50 dark:bg-secondary-900/20 border-l-4 border-secondary-500'} text-neutral-900 dark:text-neutral-100 p-3 rounded-r-lg theme-transition">
                {#if message.metadata && message.metadata.patientAnalysis && message.sender === 'therapist'}
                  <div class="text-xs font-bold text-primary-600 dark:text-primary-400 mb-1">Patient Analysis:</div>
                
                  <span class="text-xs font-bold">Sentiment: </span>
                  <span class="text-xs font-bold px-2 py-1 rounded uppercase {
                    message.metadata.patientAnalysis.sentiment === 'positive' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                    message.metadata.patientAnalysis.sentiment === 'negative' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
                    'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300'
                    } theme-transition">
                    {message.metadata.patientAnalysis.sentiment}
                  </span>
                  
                  <span class="text-xs font-bold ml-1">Motivation: </span>
                  <span class="text-xs font-bold px-2 py-1 rounded uppercase {
                      message.metadata.patientAnalysis.motivationLevel === 'low' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                      message.metadata.patientAnalysis.motivationLevel === 'medium' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                      'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                      } theme-transition">
                    {message.metadata.patientAnalysis.motivationLevel}
                  </span>
                  
                  <span class="text-xs font-bold ml-1">Engagement: </span>
                  <span class="text-xs font-bold px-2 py-1 rounded uppercase {
                      message.metadata.patientAnalysis.engagementLevel === 'low' ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-700 dark:text-yellow-300' :
                      message.metadata.patientAnalysis.engagementLevel === 'medium' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
                      'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                      } theme-transition">
                    {message.metadata.patientAnalysis.engagementLevel}
                  </span>

                  <!-- Divider -->
                  <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
                {/if}
                {message.content}
              </div>
            </div>
          {/each}

          <!-- Show spinner while conversationActive = true -->
          {#if conversationActive}
            <div role="status" class="text-center">
              <svg aria-hidden="true" class="inline w-6 h-6 text-neutral-300 dark:text-neutral-600 animate-spin fill-primary-600 dark:fill-primary-400 theme-transition" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                  <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
              </svg>
              <span class="sr-only">Loading...</span>
            </div>
          {:else if messages.length === 0}
            <div class="flex items-center justify-center h-full text-sm text-neutral-500 dark:text-neutral-400 theme-transition">
              <p>Click "Start" to begin...</p>
            </div>
          {/if}
        </div>

        <!-- Client Analytics Section -->
        <div class="analytics-section">
          <div class="analytics-header">
            Client State Analytics
          </div>
          {#if preConversationAnalytics.sentiment || postConversationAnalytics.sentiment}
            <div class="analytics-grid">
              <!-- Pre-Conversation Column -->
              <div class="analytics-column">
                <div class="analytics-column-title">Pre-Conversation</div>
                <div class="analytics-metric">
                  <span class="analytics-metric-label">Sentiment:</span>
                  <span class="analytics-metric-value {preConversationAnalytics.sentiment || 'neutral'}">
                    {preConversationAnalytics.sentiment || 'N/A'}
                  </span>
                </div>
                <div class="analytics-metric">
                  <span class="analytics-metric-label">Motivation:</span>
                  <span class="analytics-metric-value {preConversationAnalytics.motivationLevel || 'neutral'}">
                    {preConversationAnalytics.motivationLevel || 'N/A'}
                  </span>
                </div>
                <div class="analytics-metric">
                  <span class="analytics-metric-label">Engagement:</span>
                  <span class="analytics-metric-value {preConversationAnalytics.engagementLevel || 'neutral'}">
                    {preConversationAnalytics.engagementLevel || 'N/A'}
                  </span>
                </div>
              </div>

              <!-- Post-Conversation Column -->
              <div class="analytics-column">
                <div class="analytics-column-title">Current State</div>
                <div class="analytics-metric">
                  <span class="analytics-metric-label">Sentiment:</span>
                  <span class="analytics-metric-value {postConversationAnalytics.sentiment || 'neutral'}">
                    {postConversationAnalytics.sentiment || 'N/A'}
                  </span>
                </div>
                <div class="analytics-metric">
                  <span class="analytics-metric-label">Motivation:</span>
                  <span class="analytics-metric-value {postConversationAnalytics.motivationLevel || 'neutral'}">
                    {postConversationAnalytics.motivationLevel || 'N/A'}
                  </span>
                </div>
                <div class="analytics-metric">
                  <span class="analytics-metric-label">Engagement:</span>
                  <span class="analytics-metric-value {postConversationAnalytics.engagementLevel || 'neutral'}">
                    {postConversationAnalytics.engagementLevel || 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          {:else}
            <div class="analytics-no-data">
              Analytics will appear here during conversation...
            </div>
          {/if}
        </div>
      </div>

      <!-- Right Pane: Patient Profile & Thinking -->
      <div class="card p-6 flex flex-col">
        <!-- Patient Profile Section -->
        <div class="profile-section">
          <div class="profile-header">
            <!-- <div class="profile-avatar patient">
              {patientProfile.name.split(' ').map(n => n[0]).join('')}
            </div> -->
            <div>
              <h3 class="profile-name">{patientProfile.name}</h3>
              <p class="profile-title">Age {patientProfile.age}</p>
            </div>
          </div>
          <div class="profile-details">
            <div class="profile-detail-item">
              <span class="profile-detail-label">Session:</span>
              <span class="profile-detail-value">{patientProfile.sessionHistory}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Focus Area:</span>
              <span class="profile-detail-value">{patientProfile.background}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Traits:</span>
              <span class="profile-detail-value">{patientProfile.traits}</span>
            </div>
            <div class="profile-detail-item">
              <span class="profile-detail-label">Last Session:</span>
              <span class="profile-detail-value">{patientProfile.lastSession}</span>
            </div>
          </div>
        </div>

        <!-- Patient Thinking Section -->
        <div class="flex-1 flex flex-col">
          <h2 class="text-lg font-semibold text-neutral-900 dark:text-neutral-100 mb-4 flex items-center theme-transition">
            Patient Thinking
          </h2>
          <div class="space-y-3 flex-1 overflow-y-auto">
          {#each patientThoughts as thought}
            <div class="thinking-patient dark:bg-secondary-900/20 dark:text-purple-100 p-3 rounded-lg theme-transition">
              <p class="text-sm">{thought.content}</p>
              <hr class="border-neutral-300 dark:border-neutral-600 my-2 theme-transition" />
              <div class="mt-2 text-xs">
                <span class="font-medium">Time:</span> {new Date(thought.timestamp).toLocaleTimeString()}
              </div>
            </div>
          {/each}

          {#if patientThoughts.length === 0}
            <div class="flex items-center justify-center h-full text-neutral-500 dark:text-neutral-400 theme-transition">
              <p class="text-sm">Patient thinking will appear here during conversation...</p>
            </div>
          {/if}
          </div>
        </div>
      </div>
      
    </div>
  </div>
</div>
